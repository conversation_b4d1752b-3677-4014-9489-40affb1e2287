'use client';

import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useAuth } from './useAuth.js';
import { AuthContextType, AuthState } from './types.js';

const AuthContext = createContext<AuthContextType | null>(null);

interface AuthProviderProps {
  children: ReactNode;
  onAuthStateChange?: (authState: AuthState) => void;
}

export function AuthProvider({ children, onAuthStateChange }: AuthProviderProps) {
  const auth = useAuth();

  // Call the optional callback when auth state changes
  useEffect(() => {
    if (onAuthStateChange) {
      onAuthStateChange({
        user: auth.user,
        isAuthenticated: auth.isAuthenticated,
        isLoading: auth.isLoading,
        error: auth.error,
      });
    }
  }, [auth.user, auth.isAuthenticated, auth.isLoading, auth.error, onAuthStateChange]);

  const contextValue: AuthContextType = {
    login: auth.login,
    logout: auth.logout,
    refreshAuth: auth.refreshAuth,
    checkAuthStatus: auth.checkAuthStatus,
    clearError: auth.clearError,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuthContext() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
}
