import { <PERSON>r, <PERSON>, <PERSON><PERSON><PERSON>, LoginResponse, RefreshTokenResponse, APIResponse } from './types.js';

// PayloadCMS API client configuration
export class PayloadClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string = 'http://localhost:3000') {
    this.baseURL = baseURL;
    this.loadTokenFromStorage();
  }

  private loadTokenFromStorage() {
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('payload-token');
    }
  }

  private saveTokenToStorage(token: string) {
    if (typeof window !== 'undefined') {
      localStorage.setItem('payload-token', token);
    }
    this.token = token;
  }

  private removeTokenFromStorage() {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('payload-token');
    }
    this.token = null;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}/api${endpoint}`;
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string>),
    };

    if (this.token) {
      headers.Authorization = `JWT ${this.token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  // Authentication methods
  async login(email: string, password: string): Promise<LoginResponse> {
    const result = await this.request<LoginResponse>('/users/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });

    this.saveTokenToStorage(result.token);
    return result;
  }

  async logout(): Promise<void> {
    try {
      await this.request('/users/logout', { method: 'POST' });
    } finally {
      this.removeTokenFromStorage();
    }
  }

  async getCurrentUser(): Promise<User | null> {
    if (!this.token) return null;

    try {
      const result = await this.request<{ user: User }>('/users/me');
      return result.user;
    } catch (error) {
      console.error('Failed to get current user:', error);
      this.removeTokenFromStorage();
      return null;
    }
  }

  async refreshToken(): Promise<RefreshTokenResponse | null> {
    if (!this.token) return null;

    try {
      const result = await this.request<RefreshTokenResponse>('/users/refresh-token', {
        method: 'POST',
      });

      this.saveTokenToStorage(result.token);
      return result;
    } catch (error) {
      console.error('Failed to refresh token:', error);
      this.removeTokenFromStorage();
      return null;
    }
  }

  // Brand methods
  async getBrands(userId?: number): Promise<APIResponse<Brand>> {
    const params = new URLSearchParams();
    if (userId) {
      params.append('where[id][in]', userId.toString());
    }
    
    return this.request<APIResponse<Brand>>(`/brands?${params.toString()}`);
  }

  async getBrandsByUser(userId: number): Promise<APIResponse<Brand>> {
    // Get user with populated brands
    const user = await this.request<User>(`/users/${userId}?depth=1`);
    const brandIds = Array.isArray(user.brands) 
      ? user.brands.map(brand => typeof brand === 'object' ? brand.id : brand)
      : [];

    if (brandIds.length === 0) {
      return { docs: [] };
    }

    const params = new URLSearchParams();
    params.append('where[id][in]', brandIds.join(','));
    
    return this.request<APIResponse<Brand>>(`/brands?${params.toString()}`);
  }

  async createBrand(brandData: Partial<Brand>): Promise<Brand> {
    return this.request<Brand>('/brands', {
      method: 'POST',
      body: JSON.stringify(brandData),
    });
  }

  // Brand Asset methods
  async getBrandAssets(brandId?: number): Promise<APIResponse<BrandAsset>> {
    if (!brandId) {
      return this.request<APIResponse<BrandAsset>>('/brandAssets');
    }

    // Get brand with populated brand assets
    const brand = await this.request<Brand>(`/brands/${brandId}?depth=1`);
    const assetIds = Array.isArray(brand.brandAssets)
      ? brand.brandAssets.map(asset => typeof asset === 'object' ? asset.id : asset)
      : [];

    if (assetIds.length === 0) {
      return { docs: [] };
    }

    const params = new URLSearchParams();
    params.append('where[id][in]', assetIds.join(','));
    
    return this.request<APIResponse<BrandAsset>>(`/brandAssets?${params.toString()}`);
  }

  async getBrandAsset(id: number): Promise<BrandAsset> {
    return this.request<BrandAsset>(`/brandAssets/${id}`);
  }

  async createBrandAsset(assetData: Partial<BrandAsset>): Promise<BrandAsset> {
    return this.request<BrandAsset>('/brandAssets', {
      method: 'POST',
      body: JSON.stringify(assetData),
    });
  }

  async updateBrandAsset(id: number, assetData: Partial<BrandAsset>): Promise<BrandAsset> {
    return this.request<BrandAsset>(`/brandAssets/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(assetData),
    });
  }

  async deleteBrandAsset(id: number): Promise<void> {
    await this.request(`/brandAssets/${id}`, {
      method: 'DELETE',
    });
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!this.token;
  }

  getToken(): string | null {
    return this.token;
  }
}

// Create a singleton instance
export const payloadClient = new PayloadClient();
