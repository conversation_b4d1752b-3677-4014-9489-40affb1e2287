// Payload CMS types - copied from apps/cms/src/payload-types.ts
export interface User {
  id: number;
  name?: string | null;
  brands: (number | Brand)[];
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  sessions?:
    | {
        id: string;
        createdAt?: string | null;
        expiresAt: string;
      }[]
    | null;
  password?: string | null;
}

export interface Brand {
  id: number;
  name: string;
  description: string;
  visualStyle?: string | null;
  brandAssets?: (number | BrandAsset)[] | null;
  updatedAt: string;
  createdAt: string;
}

export interface BrandAsset {
  id: number;
  brandColors?: {
    primary?: string | null;
    secondary?: string | null;
    accent?: string | null;
  };
  brandFonts?: {
    primary?: string | null;
    secondary?: string | null;
  };
  graphicAssets?: {
    useGradients?: boolean | null;
    useGlowEffects?: boolean | null;
    useThickBorders?: boolean | null;
  };
  productInfo?: {
    name?: string | null;
    price?: string | null;
    variants?:
      | {
          variant?: string | null;
          id?: string | null;
        }[]
      | null;
    description?: string | null;
  };
  updatedAt: string;
  createdAt: string;
}

// Authentication state interface
export interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

// Authentication context interface
export interface AuthContextType {
  login: (email: string, password: string) => Promise<any>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<any>;
  checkAuthStatus: () => Promise<void>;
  clearError: () => void;
}

// API state interface
export interface APIState {
  isLoading: boolean;
  error: string | null;
}

// Login response interface
export interface LoginResponse {
  user: User;
  token: string;
}

// Refresh token response interface
export interface RefreshTokenResponse {
  user: User;
  token: string;
}

// API response wrapper
export interface APIResponse<T> {
  docs?: T[];
  data?: T;
  message?: string;
  errors?: Array<{
    message: string;
    field?: string;
  }>;
}
