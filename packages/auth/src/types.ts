// Re-export payload types for convenience
export type { User, Brand, BrandAsset } from '../../../apps/cms/src/payload-types';

// Authentication state interface
export interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

// Authentication context interface
export interface AuthContextType {
  login: (email: string, password: string) => Promise<any>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<any>;
  checkAuthStatus: () => Promise<void>;
  clearError: () => void;
}

// API state interface
export interface APIState {
  isLoading: boolean;
  error: string | null;
}

// Login response interface
export interface LoginResponse {
  user: User;
  token: string;
}

// Refresh token response interface
export interface RefreshTokenResponse {
  user: User;
  token: string;
}

// API response wrapper
export interface APIResponse<T> {
  docs?: T[];
  data?: T;
  message?: string;
  errors?: Array<{
    message: string;
    field?: string;
  }>;
}
