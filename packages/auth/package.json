{"name": "@repo/auth", "version": "0.0.0", "type": "module", "private": true, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/", "check-types": "tsc --noEmit"}, "peerDependencies": {"react": "^19.1.0"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/react": "19.1.0", "eslint": "^9.31.0", "typescript": "5.8.2"}}