{"name": "@repo/types", "version": "0.0.0", "type": "module", "private": true, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./payload-types": {"types": "./dist/payload-types.d.ts", "default": "./dist/payload-types.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/", "check-types": "tsc --noEmit"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "eslint": "^9.31.0", "typescript": "5.8.2"}}