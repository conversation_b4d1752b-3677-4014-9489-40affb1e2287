import { create } from 'zustand';
import { User, Brand, BrandAsset } from '@repo/auth';

export interface BrandColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
}

export interface BrandFonts {
  primary: string;
  secondary: string;
}

export interface GraphicAssets {
  useGradients: boolean;
  useGlowEffects: boolean;
  useThickBorders: boolean;
}

export interface ProductInfo {
  name: string;
  price: string;
  variants: string[];
  description: string;
}

export interface DynamicField {
  Field: string;
  Placeholder: string;
  value?: string;
}

export interface BusinessInput {
  name: string;
  description: string;
  visualStyle: string;
  dynamicFields: DynamicField[];
}

export interface SSEStatus {
  connected: boolean;
  lastUpdated: Date | null;
  status: 'disconnected' | 'connecting' | 'connected' | 'error';
  error: string | null;
}

export type AppMode = 'create' | 'edit';

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface BrandSelection {
  selectedBrandId: number | null;
  selectedBrandAssetId: number | null;
  availableBrands: Brand[];
  availableBrandAssets: BrandAsset[];
}

export interface DatabaseOperationState {
  isLoading: boolean;
  error: string | null;
  lastOperation: 'create' | 'update' | 'delete' | null;
}

export interface BrandingState {
  // Input data
  businessInput: BusinessInput;

  // AI Generated data
  brandColors: BrandColors;
  brandFonts: BrandFonts;
  graphicAssets: GraphicAssets;
  productInfo: ProductInfo;

  // SSE status
  sseStatus: SSEStatus;

  // New state for PayloadCMS integration
  appMode: AppMode;
  authState: AuthState;
  brandSelection: BrandSelection;
  databaseOperation: DatabaseOperationState;

  // Actions
  updateBusinessInput: (input: Partial<BusinessInput>) => void;
  updateBrandColors: (colors: Partial<BrandColors>) => void;
  updateBrandFonts: (fonts: Partial<BrandFonts>) => void;
  updateGraphicAssets: (assets: Partial<GraphicAssets>) => void;
  updateProductInfo: (info: Partial<ProductInfo>) => void;
  updateSSEStatus: (status: Partial<SSEStatus>) => void;
  updateDynamicFields: (fields: DynamicField[]) => void;
  updateDynamicFieldValue: (index: number, value: string) => void;
  resetBrandingData: () => void;

  // New actions for PayloadCMS integration
  setAppMode: (mode: AppMode) => void;
  updateAuthState: (authState: Partial<AuthState>) => void;
  updateBrandSelection: (selection: Partial<BrandSelection>) => void;
  updateDatabaseOperation: (operation: Partial<DatabaseOperationState>) => void;
  loadBrandAssetData: (brandAsset: BrandAsset) => void;
  resetToCreateMode: () => void;
}

const initialState = {
  businessInput: {
    name: '',
    description: '',
    visualStyle: '',
    dynamicFields: [],
  },
  brandColors: {
    primary: '#3B82F6',
    secondary: '#8B5CF6',
    accent: '#F59E0B',
    background: '#FFFFFF',
    text: '#1F2937',
  },
  brandFonts: {
    primary: 'Inter',
    secondary: 'Roboto',
  },
  graphicAssets: {
    useGradients: true,
    useGlowEffects: false,
    useThickBorders: false,
  },
  productInfo: {
    name: 'Premium Product',
    price: '$99.99',
    variants: ['Standard', 'Premium', 'Enterprise'],
    description: 'A high-quality product designed for modern businesses',
  },
  sseStatus: {
    connected: false,
    lastUpdated: null,
    status: 'disconnected' as const,
    error: null,
  },
  appMode: 'create' as AppMode,
  authState: {
    user: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
  },
  brandSelection: {
    selectedBrandId: null,
    selectedBrandAssetId: null,
    availableBrands: [],
    availableBrandAssets: [],
  },
  databaseOperation: {
    isLoading: false,
    error: null,
    lastOperation: null,
  },
};

export const useBrandingStore = create<BrandingState>((set) => ({
  ...initialState,
  
  updateBusinessInput: (input) =>
    set((state) => ({
      businessInput: { ...state.businessInput, ...input },
    })),
    
  updateBrandColors: (colors) =>
    set((state) => ({
      brandColors: { ...state.brandColors, ...colors },
    })),
      
  updateBrandFonts: (fonts) =>
    set((state) => ({
      brandFonts: { ...state.brandFonts, ...fonts },
    })),
    
  updateGraphicAssets: (assets) =>
    set((state) => ({
      graphicAssets: { ...state.graphicAssets, ...assets },
    })),
    
  updateProductInfo: (info) =>
    set((state) => ({
      productInfo: { ...state.productInfo, ...info },
    })),
    
  updateSSEStatus: (status: Partial<SSEStatus>) =>
    set((state) => ({
      sseStatus: { ...state.sseStatus, ...status },
    })),

  updateDynamicFields: (fields) =>
    set((state) => ({
      businessInput: {
        ...state.businessInput,
        dynamicFields: fields.map(field => ({ ...field, value: field.value || '' }))
      },
    })),

  updateDynamicFieldValue: (index, value) =>
    set((state) => ({
      businessInput: {
        ...state.businessInput,
        dynamicFields: state.businessInput.dynamicFields.map((field, i) =>
          i === index ? { ...field, value } : field
        ),
      },
    })),

  resetBrandingData: () => set(initialState),

  // New actions for PayloadCMS integration
  setAppMode: (mode) =>
    set(() => ({
      appMode: mode,
    })),

  updateAuthState: (authState) =>
    set((state) => ({
      authState: { ...state.authState, ...authState },
    })),

  updateBrandSelection: (selection) =>
    set((state) => ({
      brandSelection: { ...state.brandSelection, ...selection },
    })),

  updateDatabaseOperation: (operation) =>
    set((state) => ({
      databaseOperation: { ...state.databaseOperation, ...operation },
    })),

  loadBrandAssetData: (brandAsset) =>
    set((state) => ({
      appMode: 'edit' as AppMode,
      brandColors: {
        primary: brandAsset.brandColors?.primary || state.brandColors.primary,
        secondary: brandAsset.brandColors?.secondary || state.brandColors.secondary,
        accent: brandAsset.brandColors?.accent || state.brandColors.accent,
        background: state.brandColors.background,
        text: state.brandColors.text,
      },
      brandFonts: {
        primary: brandAsset.brandFonts?.primary || state.brandFonts.primary,
        secondary: brandAsset.brandFonts?.secondary || state.brandFonts.secondary,
      },
      graphicAssets: {
        useGradients: brandAsset.graphicAssets?.useGradients ?? state.graphicAssets.useGradients,
        useGlowEffects: brandAsset.graphicAssets?.useGlowEffects ?? state.graphicAssets.useGlowEffects,
        useThickBorders: brandAsset.graphicAssets?.useThickBorders ?? state.graphicAssets.useThickBorders,
      },
      productInfo: {
        name: brandAsset.productInfo?.name || state.productInfo.name,
        price: brandAsset.productInfo?.price || state.productInfo.price,
        variants: Array.isArray(brandAsset.productInfo?.variants)
          ? brandAsset.productInfo.variants.map(v => typeof v === 'string' ? v : v.variant || '')
          : state.productInfo.variants,
        description: brandAsset.productInfo?.description || state.productInfo.description,
      },
      brandSelection: {
        ...state.brandSelection,
        selectedBrandAssetId: brandAsset.id,
      },
    })),

  resetToCreateMode: () =>
    set((state) => ({
      appMode: 'create' as AppMode,
      brandSelection: {
        ...state.brandSelection,
        selectedBrandAssetId: null,
      },
      // Reset to initial branding data
      brandColors: initialState.brandColors,
      brandFonts: initialState.brandFonts,
      graphicAssets: initialState.graphicAssets,
      productInfo: initialState.productInfo,
    })),
}));
