'use client';

import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useBrandingStore } from '@/store/brandingStore';
import { usePayloadAPI } from '@/hooks/usePayloadAPI';
import { useAuthContext } from './AuthProvider';
import { BrandAsset } from '@repo/types';
import { 
  Building2, 
  Package, 
  Plus, 
  Save, 
  Edit, 
  Loader2, 
  AlertCircle,
  LogOut,
  User as UserIcon
} from 'lucide-react';

export function HeaderComponent() {
  const {
    appMode,
    authState,
    brandSelection,
    databaseOperation,
    setAppMode,
    updateBrandSelection,
    updateDatabaseOperation,
    loadBrandAssetData,
    resetToCreateMode,
    brandColors,
    brandFonts,
    graphicAssets,
    productInfo,
  } = useBrandingStore();

  const {
    fetchBrandsByUser,
    fetchBrandAssets,
    fetchBrandAsset,
    createBrandAsset,
    updateBrandAsset,
    isLoading: apiLoading,
    error: apiError,
    clearError,
  } = usePayloadAPI();

  const { logout } = useAuthContext();

  const [isLoadingBrands, setIsLoadingBrands] = useState(false);
  const [isLoadingAssets, setIsLoadingAssets] = useState(false);

  // Load user's brands when authenticated
  useEffect(() => {
    if (authState.isAuthenticated && authState.user) {
      loadUserBrands();
    }
  }, [authState.isAuthenticated, authState.user]);

  // Load brand assets when brand is selected
  useEffect(() => {
    if (brandSelection.selectedBrandId) {
      loadBrandAssets();
    }
  }, [brandSelection.selectedBrandId]);

  const loadUserBrands = async () => {
    if (!authState.user) return;

    try {
      setIsLoadingBrands(true);
      const brands = await fetchBrandsByUser(authState.user.id);
      updateBrandSelection({ availableBrands: brands });
    } catch (error) {
      console.error('Failed to load brands:', error);
    } finally {
      setIsLoadingBrands(false);
    }
  };

  const loadBrandAssets = async () => {
    if (!brandSelection.selectedBrandId) return;

    try {
      setIsLoadingAssets(true);
      const assets = await fetchBrandAssets(brandSelection.selectedBrandId);
      updateBrandSelection({ availableBrandAssets: assets });
    } catch (error) {
      console.error('Failed to load brand assets:', error);
    } finally {
      setIsLoadingAssets(false);
    }
  };

  const handleBrandSelect = (brandId: string) => {
    const selectedId = parseInt(brandId);
    updateBrandSelection({
      selectedBrandId: selectedId,
      selectedBrandAssetId: null,
      availableBrandAssets: [],
    });
    resetToCreateMode();
  };

  const handleBrandAssetSelect = async (assetId: string) => {
    if (assetId === 'create-new') {
      resetToCreateMode();
      return;
    }

    try {
      const selectedId = parseInt(assetId);
      const asset = await fetchBrandAsset(selectedId);
      loadBrandAssetData(asset);
    } catch (error) {
      console.error('Failed to load brand asset:', error);
    }
  };

  const handleSave = async () => {
    if (!brandSelection.selectedBrandId) {
      alert('Please select a brand first');
      return;
    }

    try {
      updateDatabaseOperation({ isLoading: true, error: null });

      const assetData: Partial<BrandAsset> = {
        brandColors: {
          primary: brandColors.primary,
          secondary: brandColors.secondary,
          accent: brandColors.accent,
        },
        brandFonts: {
          primary: brandFonts.primary,
          secondary: brandFonts.secondary,
        },
        graphicAssets: {
          useGradients: graphicAssets.useGradients,
          useGlowEffects: graphicAssets.useGlowEffects,
          useThickBorders: graphicAssets.useThickBorders,
        },
        productInfo: {
          name: productInfo.name,
          price: productInfo.price,
          variants: productInfo.variants.map(variant => ({ variant })),
          description: productInfo.description,
        },
      };

      if (appMode === 'create') {
        const newAsset = await createBrandAsset(assetData);
        updateBrandSelection({ selectedBrandAssetId: newAsset.id });
        setAppMode('edit');
        // Reload brand assets to include the new one
        await loadBrandAssets();
      } else {
        if (!brandSelection.selectedBrandAssetId) {
          throw new Error('No brand asset selected for update');
        }
        await updateBrandAsset(brandSelection.selectedBrandAssetId, assetData);
      }

      updateDatabaseOperation({ 
        isLoading: false, 
        lastOperation: appMode === 'create' ? 'create' : 'update' 
      });
    } catch (error) {
      console.error('Save failed:', error);
      updateDatabaseOperation({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Save failed',
      });
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      updateBrandSelection({
        selectedBrandId: null,
        selectedBrandAssetId: null,
        availableBrands: [],
        availableBrandAssets: [],
      });
      resetToCreateMode();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  if (!authState.isAuthenticated) {
    return (
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-center">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-amber-500 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Please log in to access brand management</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white border-b px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left Side - Brand Selection */}
        <div className="flex items-center space-x-6">
          {/* Brand Selector */}
          <div className="flex items-center space-x-2">
            <Building2 className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Brand:</span>
            <Select
              value={brandSelection.selectedBrandId?.toString() || ''}
              onValueChange={handleBrandSelect}
              disabled={isLoadingBrands}
            >
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Select a brand" />
              </SelectTrigger>
              <SelectContent>
                {brandSelection.availableBrands.map((brand) => (
                  <SelectItem key={brand.id} value={brand.id.toString()}>
                    {brand.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {isLoadingBrands && <Loader2 className="h-4 w-4 animate-spin text-gray-400" />}
          </div>

          {/* Brand Asset Selector */}
          {brandSelection.selectedBrandId && (
            <div className="flex items-center space-x-2">
              <Package className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Asset:</span>
              <Select
                value={brandSelection.selectedBrandAssetId?.toString() || 'create-new'}
                onValueChange={handleBrandAssetSelect}
                disabled={isLoadingAssets}
              >
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="create-new">
                    <div className="flex items-center space-x-2">
                      <Plus className="h-4 w-4" />
                      <span>Create New Asset</span>
                    </div>
                  </SelectItem>
                  {brandSelection.availableBrandAssets.map((asset) => (
                    <SelectItem key={asset.id} value={asset.id.toString()}>
                      Asset #{asset.id}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {isLoadingAssets && <Loader2 className="h-4 w-4 animate-spin text-gray-400" />}
            </div>
          )}

          {/* Mode Badge */}
          <Badge variant={appMode === 'create' ? 'default' : 'secondary'}>
            {appMode === 'create' ? 'Create Mode' : 'Edit Mode'}
          </Badge>
        </div>

        {/* Right Side - User Info and Actions */}
        <div className="flex items-center space-x-4">
          {/* Error Display */}
          {(databaseOperation.error || apiError) && (
            <div className="flex items-center space-x-2 text-red-600">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{databaseOperation.error || apiError}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  updateDatabaseOperation({ error: null });
                  clearError();
                }}
              >
                ×
              </Button>
            </div>
          )}

          {/* Save/Update Button */}
          {brandSelection.selectedBrandId && (
            <Button
              onClick={handleSave}
              disabled={databaseOperation.isLoading || apiLoading}
              className="flex items-center space-x-2"
            >
              {databaseOperation.isLoading || apiLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : appMode === 'create' ? (
                <Save className="h-4 w-4" />
              ) : (
                <Edit className="h-4 w-4" />
              )}
              <span>{appMode === 'create' ? 'Save' : 'Update'}</span>
            </Button>
          )}

          {/* User Info */}
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <UserIcon className="h-4 w-4" />
            <span>{authState.user?.name || authState.user?.email}</span>
          </div>

          {/* Logout Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleLogout}
            className="flex items-center space-x-1"
          >
            <LogOut className="h-4 w-4" />
            <span>Logout</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
