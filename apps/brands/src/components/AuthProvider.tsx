'use client';

import React from 'react';
import { AuthProvider as SharedAuthProvider, useAuthContext } from '@repo/auth';
import { useBrandingStore } from '../store/brandingStore';
import { AuthState } from '@repo/auth';

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const { updateAuthState } = useBrandingStore();

  // Callback to sync auth state with Zustand store
  const handleAuthStateChange = (authState: AuthState) => {
    updateAuthState({
      user: authState.user,
      isAuthenticated: authState.isAuthenticated,
      isLoading: authState.isLoading,
      error: authState.error,
    });
  };

  return (
    <SharedAuthProvider onAuthStateChange={handleAuthStateChange}>
      {children}
    </SharedAuthProvider>
  );
}

// Re-export useAuthContext for convenience
export { useAuthContext };
