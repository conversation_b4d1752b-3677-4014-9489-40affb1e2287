'use client';

import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useBrandingStore } from '../store/brandingStore';

interface AuthContextType {
  login: (email: string, password: string) => Promise<any>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<any>;
  checkAuthStatus: () => Promise<void>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | null>(null);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const auth = useAuth();
  const { updateAuthState } = useBrandingStore();

  // Sync auth state with Zustand store
  useEffect(() => {
    updateAuthState({
      user: auth.user,
      isAuthenticated: auth.isAuthenticated,
      isLoading: auth.isLoading,
      error: auth.error,
    });
  }, [auth.user, auth.isAuthenticated, auth.isLoading, auth.error, updateAuthState]);

  const contextValue: AuthContextType = {
    login: auth.login,
    logout: auth.logout,
    refreshAuth: auth.refreshAuth,
    checkAuthStatus: auth.checkAuthStatus,
    clearError: auth.clearError,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuthContext() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
}
