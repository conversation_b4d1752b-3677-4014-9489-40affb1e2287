'use client';

import React, { useCallback, useRef } from 'react';
import { AuthProvider as SharedAuthProvider, useAuthContext } from '@repo/auth';
import { useBrandingStore } from '../store/brandingStore';
import { AuthState } from '@repo/auth';

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const { updateAuthState } = useBrandingStore();
  const lastAuthStateRef = useRef<AuthState | null>(null);

  // Callback to sync auth state with Zustand store
  const handleAuthStateChange = useCallback((authState: AuthState) => {
    // Prevent infinite loops by checking if the state actually changed
    const lastState = lastAuthStateRef.current;

    if (
      !lastState ||
      lastState.user?.id !== authState.user?.id ||
      lastState.isAuthenticated !== authState.isAuthenticated ||
      lastState.isLoading !== authState.isLoading ||
      lastState.error !== authState.error
    ) {
      lastAuthStateRef.current = authState;
      updateAuthState({
        user: authState.user,
        isAuthenticated: authState.isAuthenticated,
        isLoading: authState.isLoading,
        error: authState.error,
      });
    }
  }, [updateAuthState]);

  return (
    <SharedAuthProvider onAuthStateChange={handleAuthStateChange}>
      {children}
    </SharedAuthProvider>
  );
}

// Re-export useAuthContext for convenience
export { useAuthContext };
